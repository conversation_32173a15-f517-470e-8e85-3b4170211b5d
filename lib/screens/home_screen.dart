import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:gameflex_mobile/theme/app_theme.dart';
import 'package:gameflex_mobile/components/index.dart';
import 'package:gameflex_mobile/screens/profile_screen.dart';
import 'package:gameflex_mobile/screens/upload_screen.dart';
import 'package:gameflex_mobile/screens/splash_manager.dart';
import 'package:gameflex_mobile/providers/posts_provider.dart';
import 'package:gameflex_mobile/providers/followed_posts_provider.dart';
import 'package:gameflex_mobile/providers/auth_provider.dart';
import 'package:gameflex_mobile/providers/channels_provider.dart';
import 'package:gameflex_mobile/providers/realtime_notification_provider.dart';
import 'package:gameflex_mobile/services/websocket_service.dart';
import 'package:gameflex_mobile/models/notification_model.dart';
import 'package:gameflex_mobile/utils/app_logger.dart';
import 'package:gameflex_mobile/widgets/feed.dart';
import 'package:gameflex_mobile/widgets/followed_feed.dart';
import 'package:gameflex_mobile/widgets/home_tab_with_navigation.dart';
import 'package:gameflex_mobile/widgets/reflex_drawer_overlay.dart';
import 'package:gameflex_mobile/widgets/notification_menu.dart';
import 'package:gameflex_mobile/models/post_model.dart';
import 'package:gameflex_mobile/main.dart';
import 'package:gameflex_mobile/services/config_service.dart';
import 'package:gameflex_mobile/screens/debug_auth_screen.dart';
import 'package:gameflex_mobile/screens/post_detail_screen.dart';
import 'package:gameflex_mobile/screens/user_profile_screen.dart';
import 'package:gameflex_mobile/screens/reflexes_feed_screen.dart';
import 'package:gameflex_mobile/services/aws_posts_service.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();

  /// Static method to handle notification navigation from anywhere in the app
  static Future<void> navigateToNotification(
    BuildContext context,
    RealtimeNotificationModel notification,
  ) async {
    try {
      AppLogger.navigation(
        'Deep link navigation for notification: ${notification.id}',
      );

      switch (notification.type) {
        case NotificationType.follow:
          await _navigateToUserProfile(context, notification.actorUserId);
          break;
        case NotificationType.comment:
          if (notification.postId != null) {
            await _navigateToPostDetailStatic(context, notification.postId!);
          }
          break;
        case NotificationType.reflex:
          if (notification.postId != null) {
            await _navigateToReflexFeedStatic(context, notification.postId!);
          }
          break;
      }
    } catch (e) {
      AppLogger.error('HomeScreen: Error in deep link navigation: $e');
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Unable to navigate to notification content'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  static Future<void> _navigateToUserProfile(
    BuildContext context,
    String userId,
  ) async {
    if (userId.isNotEmpty && context.mounted) {
      Navigator.of(context).push(
        MaterialPageRoute(
          builder: (context) => UserProfileScreen(userId: userId),
        ),
      );
    }
  }

  static Future<void> _navigateToPostDetailStatic(
    BuildContext context,
    String postId,
  ) async {
    try {
      PostModel? post = await AwsPostsService.instance.getPost(postId);

      if (post != null && context.mounted) {
        Navigator.of(context).push(
          MaterialPageRoute(builder: (context) => PostDetailScreen(post: post)),
        );
      }
    } catch (e) {
      AppLogger.error('HomeScreen: Error loading post for deep link: $e');
    }
  }

  static Future<void> _navigateToReflexFeedStatic(
    BuildContext context,
    String postId,
  ) async {
    try {
      PostModel? post = await AwsPostsService.instance.getPost(postId);

      if (post != null && context.mounted) {
        Navigator.of(context).push(
          MaterialPageRoute(
            builder: (context) => ReflexesFeedScreen(post: post),
          ),
        );
      }
    } catch (e) {
      AppLogger.error(
        'HomeScreen: Error loading post for reflex deep link: $e',
      );
    }
  }
}

class _HomeScreenState extends State<HomeScreen> with RouteAware {
  int _selectedIndex = 0;
  int _feedTabIndex =
      1; // 0 for Followed, 1 for Prime (default), 2 for Channels
  late final Widget _feedWidget;
  Widget? _followedFeedWidget;
  bool _showReflexDrawer = false;
  PostModel? _currentReflexPost;
  PostModel? _currentVisiblePost;
  bool _realtimeServicesInitialized = false;

  @override
  void initState() {
    super.initState();
    // Initialize the feed widget once to preserve state
    _feedWidget = Feed(
      onNavigateToReflexes: _showReflexDrawerForPost,
      onCurrentPostChanged: _updateCurrentVisiblePost,
    );

    // Load posts when the home screen is first created, but only if authenticated
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final authProvider = Provider.of<AuthProvider>(context, listen: false);
      final postsProvider = Provider.of<PostsProvider>(context, listen: false);
      final notificationProvider = Provider.of<RealtimeNotificationProvider>(
        context,
        listen: false,
      );

      // Only load posts if user is authenticated
      if (authProvider.isAuthenticated &&
          postsProvider.status == PostsStatus.initial) {
        postsProvider.loadPosts();
      }
      // Start real-time subscriptions
      postsProvider.startRealtimeSubscriptions();

      // Initialize WebSocket connection and notification provider if authenticated
      if (authProvider.isAuthenticated) {
        _initializeRealtimeServices(notificationProvider);
      }
    });
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    final route = ModalRoute.of(context);
    if (route is PageRoute) {
      MyApp.routeObserver.subscribe(this, route);
    }
  }

  @override
  void dispose() {
    MyApp.routeObserver.unsubscribe(this);
    // Disconnect WebSocket when home screen is disposed
    _disconnectRealtimeServices();
    super.dispose();
  }

  // RouteAware methods
  @override
  void didPopNext() {
    // User came back to this screen from another screen
    // Refresh the feed if we're on the home tab
    AppLogger.navigation('User returned from another screen');
    if (_selectedIndex == 0) {
      AppLogger.navigation('On home tab - refreshing feed');
      final postsProvider = Provider.of<PostsProvider>(context, listen: false);
      postsProvider.refreshPosts();
    }
  }

  @override
  void didPush() {}

  @override
  void didPop() {}

  @override
  void didPushNext() {}

  void _onItemTapped(int index) {
    // Handle video lifecycle when switching tabs
    if (_selectedIndex != index) {
      if (_selectedIndex == 0) {
        // Leaving home tab - pause all videos
        VideoLifecycleManager().onTabSwitch(true);
      } else if (index == 0) {
        // Returning to home tab - allow videos to resume based on visibility
        VideoLifecycleManager().onTabSwitch(false);
      }
    }

    // If tapping home button while already on home, refresh the current feed
    if (index == 0 && _selectedIndex == 0) {
      AppLogger.navigation(
        'Home button tapped while on home - refreshing current feed (tab: $_feedTabIndex)',
      );
      _refreshCurrentFeed();
      return;
    }

    // If navigating back to home from another tab, refresh the current feed
    if (index == 0 && _selectedIndex != 0) {
      AppLogger.navigation(
        'Navigating back to home from tab $_selectedIndex - refreshing current feed (tab: $_feedTabIndex)',
      );
      _refreshCurrentFeed();
    }

    setState(() {
      _selectedIndex = index;
    });
  }

  /// Refresh the currently active feed based on the selected feed tab
  void _refreshCurrentFeed() {
    switch (_feedTabIndex) {
      case 0:
        // Followed tab - immediate scroll to top, then refresh in background
        AppLogger.navigation(
          'Refreshing Followed feed - immediate scroll to top',
        );
        final followedPostsProvider = Provider.of<FollowedPostsProvider>(
          context,
          listen: false,
        );
        // Immediate scroll to top by resetting currentPostIndex
        followedPostsProvider.resetToTop();
        // Then refresh in background
        followedPostsProvider.refreshPosts();
        break;
      case 1:
        // Prime tab - immediate scroll to top, then refresh in background
        AppLogger.navigation('Refreshing Prime feed - immediate scroll to top');
        final postsProvider = Provider.of<PostsProvider>(
          context,
          listen: false,
        );
        // Immediate scroll to top by resetting currentPostIndex
        postsProvider.resetToTop();
        // Then refresh in background
        postsProvider.refreshPosts();
        break;
      case 2:
        // Channels tab - refresh channels
        AppLogger.navigation('Refreshing Channels');
        final channelsProvider = Provider.of<ChannelsProvider>(
          context,
          listen: false,
        );
        channelsProvider.refreshChannels();
        break;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Consumer2<AuthProvider, RealtimeNotificationProvider>(
      builder: (context, authProvider, notificationProvider, child) {
        // Handle authentication state changes
        WidgetsBinding.instance.addPostFrameCallback((_) {
          if (mounted) {
            _handleAuthStateChange(authProvider, notificationProvider);
          }
        });

        // If user is no longer authenticated, return to splash manager
        if (!authProvider.isAuthenticated) {
          // Use a post-frame callback to avoid calling Navigator during build
          WidgetsBinding.instance.addPostFrameCallback((_) {
            if (mounted) {
              Navigator.of(context).pushAndRemoveUntil(
                MaterialPageRoute(builder: (context) => const SplashManager()),
                (route) => false,
              );
            }
          });
          // Return a loading screen while navigation is happening
          return const Scaffold(
            backgroundColor: AppColors.gfGrayBlue900,
            body: Center(
              child: CircularProgressIndicator(color: AppColors.gfGreen),
            ),
          );
        }

        return Scaffold(
          appBar: _buildAppBar(),
          body: Stack(
            children: [
              _buildBody(),
              // Reflex navigation button (only show on home tab)
              if (_selectedIndex == 0) _buildReflexNavigationButton(),
              // Reflex drawer overlay
              if (_showReflexDrawer && _currentReflexPost != null)
                Positioned.fill(
                  child: ReflexDrawerOverlay(
                    post: _currentReflexPost!,
                    onClose: _closeReflexDrawer,
                    onPauseVideo: _pauseCurrentVideo,
                  ),
                ),
            ],
          ),
          bottomNavigationBar: GFSimpleBottomNavigation(
            currentIndex: _selectedIndex,
            onTap: _onItemTapped,
          ),
          // Debug FAB (only visible in development mode and on home tab)
          floatingActionButton:
              ConfigService.instance.isDevelopment && _selectedIndex == 0
                  ? FloatingActionButton(
                    mini: true,
                    backgroundColor: AppColors.gfGreen,
                    foregroundColor: AppColors.gfDarkBlue,
                    onPressed: () {
                      Navigator.of(context).push(
                        MaterialPageRoute(
                          builder: (context) => const DebugAuthScreen(),
                        ),
                      );
                    },
                    child: const Icon(Icons.bug_report),
                  )
                  : null,
        );
      },
    );
  }

  String _getTabTitle() {
    switch (_selectedIndex) {
      case 1:
        return 'Upload';
      case 2:
        return 'Profile';
      default:
        return 'GameFlex';
    }
  }

  PreferredSizeWidget? _buildAppBar() {
    // No app bar for home tab (index 0)
    if (_selectedIndex == 0) {
      return null;
    }

    return AppBar(
      title: Text(
        _getTabTitle(),
        style: const TextStyle(
          color: AppColors.gfGreen,
          fontWeight: FontWeight.bold,
        ),
      ),
      actions: [
        // Notification menu (always visible when app bar is shown)
        NotificationMenu(
          onNotificationTap: _handleNotificationTap,
          iconSize: 24,
        ),

        // Search button
        IconButton(
          icon: const Icon(Icons.search, color: AppColors.gfOffWhite),
          onPressed: () {},
        ),

        // Debug button (only visible in development mode)
        if (ConfigService.instance.isDevelopment)
          IconButton(
            icon: const Icon(Icons.bug_report, color: AppColors.gfGreen),
            onPressed: () {
              Navigator.of(context).push(
                MaterialPageRoute(
                  builder: (context) => const DebugAuthScreen(),
                ),
              );
            },
          ),
      ],
    );
  }

  void _handleNotificationTap(RealtimeNotificationModel notification) {
    AppLogger.navigation('Notification tapped: ${notification.id}');

    // Navigate based on notification type and data
    _navigateToNotificationTarget(notification);
  }

  void _navigateToNotificationTarget(RealtimeNotificationModel notification) {
    AppLogger.navigation(
      'Navigating to notification target: ${notification.type}',
    );
    AppLogger.debug('Navigation data: ${notification.data}');

    try {
      switch (notification.type) {
        case NotificationType.follow:
          _navigateToFollowNotification(notification);
          break;
        case NotificationType.comment:
          _navigateToCommentNotification(notification);
          break;
        case NotificationType.reflex:
          _navigateToReflexNotification(notification);
          break;
      }
    } catch (e) {
      AppLogger.error(
        'HomeScreen: Error navigating to notification target: $e',
      );
      _showNavigationError();
    }
  }

  void _navigateToFollowNotification(RealtimeNotificationModel notification) {
    final actorUserId = notification.actorUserId;

    if (actorUserId.isNotEmpty) {
      AppLogger.navigation('Navigating to user profile: $actorUserId');
      Navigator.of(context).push(
        MaterialPageRoute(
          builder: (context) => UserProfileScreen(userId: actorUserId),
        ),
      );
    } else {
      AppLogger.error(
        'HomeScreen: Missing actorUserId for follow notification',
      );
      _showNavigationError();
    }
  }

  void _navigateToCommentNotification(RealtimeNotificationModel notification) {
    final postId = notification.postId;
    final commentId = notification.commentId;

    if (postId != null && postId.isNotEmpty) {
      AppLogger.navigation('Navigating to post detail: $postId');
      _navigateToPostDetail(postId, highlightCommentId: commentId);
    } else {
      AppLogger.error('HomeScreen: Missing postId for comment notification');
      _showNavigationError();
    }
  }

  void _navigateToReflexNotification(RealtimeNotificationModel notification) {
    final postId = notification.postId;
    final reflexId = notification.reflexId;

    if (postId != null && postId.isNotEmpty) {
      AppLogger.navigation('Navigating to reflex feed for post: $postId');
      _navigateToReflexFeed(postId, highlightReflexId: reflexId);
    } else {
      AppLogger.error('HomeScreen: Missing postId for reflex notification');
      _showNavigationError();
    }
  }

  void _showNavigationError() {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Unable to navigate to notification content'),
          backgroundColor: Colors.red,
          duration: Duration(seconds: 3),
        ),
      );
    }
  }

  Future<void> _navigateToPostDetail(
    String postId, {
    String? highlightCommentId,
  }) async {
    try {
      // First, try to find the post in the current posts list
      final postsProvider = Provider.of<PostsProvider>(context, listen: false);
      PostModel? post;
      try {
        post = postsProvider.posts.firstWhere((p) => p.id == postId);
      } catch (e) {
        // Post not found in local list
        post = null;
      }

      // If not found locally, try to load it from the server
      if (post == null) {
        AppLogger.navigation(
          'Post not found locally, loading from server: $postId',
        );
        _showLoadingSnackBar('Loading post...');

        // Load the post from the server
        post = await AwsPostsService.instance.getPost(postId);

        if (mounted) {
          ScaffoldMessenger.of(context).hideCurrentSnackBar();
        }
      }

      if (post != null && mounted) {
        Navigator.of(context).push(
          MaterialPageRoute(
            builder: (context) => PostDetailScreen(post: post!),
          ),
        );
      } else {
        AppLogger.error('HomeScreen: Could not load post: $postId');
        _showNavigationError();
      }
    } catch (e) {
      AppLogger.error('HomeScreen: Error loading post for navigation: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).hideCurrentSnackBar();
        _showNavigationError();
      }
    }
  }

  Future<void> _navigateToReflexFeed(
    String postId, {
    String? highlightReflexId,
  }) async {
    try {
      // First, try to find the post in the current posts list
      final postsProvider = Provider.of<PostsProvider>(context, listen: false);
      PostModel? post;
      try {
        post = postsProvider.posts.firstWhere((p) => p.id == postId);
      } catch (e) {
        // Post not found in local list
        post = null;
      }

      // If not found locally, try to load it from the server
      if (post == null) {
        AppLogger.navigation(
          'Post not found locally for reflex feed, loading from server: $postId',
        );
        _showLoadingSnackBar('Loading post...');

        post = await AwsPostsService.instance.getPost(postId);

        if (mounted) {
          ScaffoldMessenger.of(context).hideCurrentSnackBar();
        }
      }

      if (post != null && mounted) {
        AppLogger.navigation('Navigating to reflex feed for post: $postId');
        Navigator.of(context).push(
          MaterialPageRoute(
            builder: (context) => ReflexesFeedScreen(post: post!),
          ),
        );
      } else {
        AppLogger.error(
          'HomeScreen: Could not load post for reflex feed: $postId',
        );
        _showNavigationError();
      }
    } catch (e) {
      AppLogger.error('HomeScreen: Error navigating to reflex feed: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).hideCurrentSnackBar();
        _showNavigationError();
      }
    }
  }

  void _showLoadingSnackBar(String message) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Row(
            children: [
              const SizedBox(
                width: 16,
                height: 16,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                ),
              ),
              const SizedBox(width: 16),
              Text(message),
            ],
          ),
          backgroundColor: AppColors.gfGrayText,
          duration: const Duration(seconds: 10), // Long duration for loading
        ),
      );
    }
  }

  Future<void> _initializeRealtimeServices(
    RealtimeNotificationProvider notificationProvider,
  ) async {
    try {
      AppLogger.info('HomeScreen: Initializing real-time services');

      // Initialize notification provider
      await notificationProvider.initialize();

      // Connect WebSocket service
      if (!WebSocketService.instance.isConnected) {
        await WebSocketService.instance.connect();
      }

      AppLogger.info('HomeScreen: Real-time services initialized successfully');
    } catch (e) {
      AppLogger.error(
        'HomeScreen: Failed to initialize real-time services: $e',
      );
      // Don't throw - app should continue to work without real-time features
    }
  }

  void _handleAuthStateChange(
    AuthProvider authProvider,
    RealtimeNotificationProvider notificationProvider,
  ) {
    if (authProvider.isAuthenticated && !_realtimeServicesInitialized) {
      // User just authenticated - initialize real-time services
      _initializeRealtimeServices(notificationProvider);
      _realtimeServicesInitialized = true;
    } else if (!authProvider.isAuthenticated && _realtimeServicesInitialized) {
      // User signed out - disconnect WebSocket
      _disconnectRealtimeServices();
      _realtimeServicesInitialized = false;
    }
  }

  Future<void> _disconnectRealtimeServices() async {
    try {
      AppLogger.info('HomeScreen: Disconnecting real-time services');
      await WebSocketService.instance.disconnect();
    } catch (e) {
      AppLogger.error('HomeScreen: Error disconnecting real-time services: $e');
    }
  }

  Widget _buildBody() {
    switch (_selectedIndex) {
      case 0:
        return _buildHomeTab();
      case 1:
        return const UploadScreen();
      case 2:
        return const ProfileScreen();
      default:
        return _buildHomeTab();
    }
  }

  Widget _buildHomeTab() {
    return Column(
      children: [
        SafeArea(bottom: false, child: _buildHomeHeader()),
        _buildFeedTabs(),
        Expanded(child: _buildFeedContent()),
      ],
    );
  }

  Widget _buildHomeHeader() {
    return Container(
      height: 56, // Standard app bar height
      decoration: const BoxDecoration(
        color: AppColors.gfDarkBlue,
        border: Border(
          bottom: BorderSide(color: AppColors.gfGrayBorder, width: 0.5),
        ),
      ),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16),
        child: Row(
          children: [
            // GameFlex logo/title
            const Text(
              'GameFlex',
              style: TextStyle(
                color: AppColors.gfGreen,
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),

            const Spacer(),

            // Notification menu
            NotificationMenu(
              onNotificationTap: _handleNotificationTap,
              iconSize: 24,
            ),

            // Search button
            IconButton(
              icon: const Icon(Icons.search, color: AppColors.gfOffWhite),
              onPressed: () {},
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFeedTabs() {
    return Container(
      height: 50,
      decoration: const BoxDecoration(
        color: AppColors.gfDarkBlue,
        border: Border(
          bottom: BorderSide(color: AppColors.gfGrayBorder, width: 0.5),
        ),
      ),
      child: Row(
        children: [
          Expanded(
            child: _buildTabButton(
              title: 'Followed',
              isSelected: _feedTabIndex == 0,
              onTap: () => _onFeedTabTapped(0),
            ),
          ),
          Container(width: 1, height: 20, color: AppColors.gfGrayBorder),
          Expanded(
            child: _buildTabButton(
              title: 'Prime',
              isSelected: _feedTabIndex == 1,
              onTap: () => _onFeedTabTapped(1),
            ),
          ),
          Container(width: 1, height: 20, color: AppColors.gfGrayBorder),
          Expanded(
            child: _buildTabButton(
              title: 'Channels',
              isSelected: _feedTabIndex == 2,
              onTap: () => _onFeedTabTapped(2),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTabButton({
    required String title,
    required bool isSelected,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      behavior: HitTestBehavior.opaque,
      child: Container(
        height: double.infinity,
        decoration: BoxDecoration(
          color:
              isSelected
                  ? AppColors.gfGreen.withValues(alpha: 0.1)
                  : Colors.transparent,
          border: Border(
            bottom: BorderSide(
              color: isSelected ? AppColors.gfGreen : Colors.transparent,
              width: 2,
            ),
          ),
        ),
        child: Center(
          child: Text(
            title,
            style: TextStyle(
              color: isSelected ? AppColors.gfGreen : AppColors.gfGrayText,
              fontSize: 16,
              fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildFeedContent() {
    switch (_feedTabIndex) {
      case 0:
        // Followed tab - show posts from followed users and channels
        return _followedFeedWidget ?? _buildFollowedFeedWidget();
      case 1:
        // Prime tab - default feed
        return _feedWidget;
      case 2:
        // Channels tab - show channels
        return const ChannelsScreenContent();
      default:
        return _feedWidget;
    }
  }

  Widget _buildFollowedFeedWidget() {
    _followedFeedWidget ??= FollowedFeed(
      onNavigateToReflexes: _showReflexDrawerForPost,
      onCurrentPostChanged: _updateCurrentVisiblePost,
    );
    return _followedFeedWidget!;
  }

  void _onFeedTabTapped(int index) {
    // Pause videos when switching between feed tabs
    if (_feedTabIndex != index) {
      VideoLifecycleManager().onTabSwitch(true);

      // Small delay to allow videos to pause, then allow resume
      Future.delayed(const Duration(milliseconds: 100), () {
        VideoLifecycleManager().onTabSwitch(false);
      });
    }

    setState(() {
      _feedTabIndex = index;
    });

    // Load followed posts when switching to followed tab
    if (index == 0) {
      final followedPostsProvider = Provider.of<FollowedPostsProvider>(
        context,
        listen: false,
      );
      if (followedPostsProvider.posts.isEmpty &&
          followedPostsProvider.status == FollowedPostsStatus.initial) {
        followedPostsProvider.loadPosts();
      }
    }

    // Load channels when switching to channels tab
    if (index == 2) {
      final channelsProvider = Provider.of<ChannelsProvider>(
        context,
        listen: false,
      );
      if (channelsProvider.channels.isEmpty &&
          channelsProvider.status == ChannelsStatus.initial) {
        channelsProvider.loadChannels();
      }
    }
  }

  Widget _buildReflexNavigationButton() {
    return Consumer<PostsProvider>(
      builder: (context, postsProvider, child) {
        // Get the current post from the provider to ensure we have the latest data
        PostModel? currentPost;
        if (_currentVisiblePost != null) {
          // Try to find the updated post in the provider
          final updatedPost = postsProvider.posts.firstWhere(
            (post) => post.id == _currentVisiblePost!.id,
            orElse: () => _currentVisiblePost!,
          );
          currentPost = updatedPost;
        } else if (postsProvider.posts.isNotEmpty &&
            postsProvider.currentPostIndex < postsProvider.posts.length) {
          currentPost = postsProvider.posts[postsProvider.currentPostIndex];
        }

        // Check if current post has reflexes
        final hasReflexes =
            currentPost?.reflexCount != null && currentPost!.reflexCount > 0;

        return Positioned(
          right: 0, // Align to the very right edge
          top:
              MediaQuery.of(context).size.height *
              0.4, // Middle right of screen
          child: GestureDetector(
            onTap: hasReflexes ? _navigateToReflexes : null,
            onPanStart:
                hasReflexes
                    ? (details) {
                      // Store initial position for swipe detection
                    }
                    : null,
            onPanEnd:
                hasReflexes
                    ? (details) {
                      // Handle swipe left gesture based on velocity
                      if (details.velocity.pixelsPerSecond.dx < -500) {
                        _navigateToReflexes();
                      }
                    }
                    : null,
            child: Container(
              width: 40,
              height: 80,
              decoration: BoxDecoration(
                color:
                    hasReflexes
                        ? AppColors.gfGreen.withValues(alpha: 0.9)
                        : AppColors.gfGrayText.withValues(alpha: 0.3),
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(20),
                  bottomLeft: Radius.circular(20),
                ),
                boxShadow:
                    hasReflexes
                        ? [
                          BoxShadow(
                            color: Colors.black.withValues(alpha: 0.3),
                            blurRadius: 8,
                            offset: const Offset(-2, 0),
                          ),
                        ]
                        : [],
              ),
              child: Center(
                child: Icon(
                  Icons.keyboard_arrow_left,
                  color:
                      hasReflexes
                          ? Colors.black
                          : Colors.grey.withValues(alpha: 0.5),
                  size: 24,
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  void _navigateToReflexes() {
    // Use the current visible post if available
    if (_currentVisiblePost != null) {
      // Check if the current post has reflexes
      if (_currentVisiblePost!.reflexCount > 0) {
        setState(() {
          _currentReflexPost = _currentVisiblePost;
          _showReflexDrawer = true;
        });
      } else {
        // Show a message if no reflexes are available
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('No reflexes available for this post'),
            backgroundColor: AppColors.gfGrayText,
          ),
        );
      }
    } else {
      // Fallback to provider's current post
      final postsProvider = Provider.of<PostsProvider>(context, listen: false);

      if (postsProvider.posts.isNotEmpty &&
          postsProvider.currentPostIndex < postsProvider.posts.length) {
        final currentPost = postsProvider.posts[postsProvider.currentPostIndex];

        // Check if the current post has reflexes
        if (currentPost.reflexCount > 0) {
          setState(() {
            _currentReflexPost = currentPost;
            _showReflexDrawer = true;
          });
        } else {
          // Show a message if no reflexes are available
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('No reflexes available for this post'),
              backgroundColor: AppColors.gfGrayText,
            ),
          );
        }
      } else {
        // Show a message if no post is available
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('No post available to view reflexes'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _closeReflexDrawer() {
    // Allow videos to resume when reflex drawer closes
    VideoLifecycleManager().onReflexDrawerOpen(false);

    setState(() {
      _showReflexDrawer = false;
      _currentReflexPost = null;
    });
  }

  void _showReflexDrawerForPost(PostModel post) {
    // Pause videos when reflex drawer opens
    VideoLifecycleManager().onReflexDrawerOpen(true);

    setState(() {
      _currentReflexPost = post;
      _showReflexDrawer = true;
    });
  }

  void _updateCurrentVisiblePost(PostModel post) {
    setState(() {
      _currentVisiblePost = post;
    });
  }

  void _pauseCurrentVideo() {
    // This will pause any currently playing video in the feed
    // The Feed widget will handle the actual video pausing through visibility changes
    AppLogger.debug('HomeScreen: Pausing current video for reflex drawer');
  }
}
